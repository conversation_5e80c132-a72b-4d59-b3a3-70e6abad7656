<template>
  <div 
    class="tool-card cursor-pointer group"
    @click="goToDetail"
  >
    <!-- 工具图标和基本信息 -->
    <div class="flex items-start space-x-3 mb-3">
      <div class="flex-shrink-0">
        <div class="w-12 h-12 rounded-xl bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white text-xl shadow-lg">
          {{ tool.icon }}
        </div>
      </div>
      <div class="flex-1 min-w-0">
        <h3 class="font-semibold text-gray-900 text-lg mb-1 truncate group-hover:text-primary transition-colors">
          {{ tool.name }}
        </h3>
        <p class="text-gray-600 text-sm line-clamp-2 leading-relaxed">
          {{ tool.description }}
        </p>
      </div>
      <!-- 收藏按钮 -->
      <button
        @click.stop="toggleFavorite"
        class="flex-shrink-0 p-2 rounded-lg hover:bg-gray-100 transition-colors"
        :class="[isFavorited ? 'text-red-500' : 'text-gray-400']"
      >
        <icon-heart :fill="isFavorited" class="w-5 h-5" />
      </button>
    </div>

    <!-- 标签 -->
    <div class="flex flex-wrap gap-1 mb-3">
      <span
        v-for="tag in tool.tags.slice(0, 3)"
        :key="tag"
        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-secondary-100 text-primary-700"
      >
        {{ tag }}
      </span>
      <span
        v-if="tool.tags.length > 3"
        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600"
      >
        +{{ tool.tags.length - 3 }}
      </span>
    </div>

    <!-- 底部信息 -->
    <div class="flex items-center justify-between text-sm text-gray-500">
      <div class="flex items-center space-x-4">
        <!-- 评分 -->
        <div class="flex items-center space-x-1">
          <icon-star-fill class="w-4 h-4 text-yellow-400" />
          <span class="font-medium">{{ tool.rating }}</span>
        </div>
        <!-- 下载量 -->
        <div class="flex items-center space-x-1">
          <icon-download class="w-4 h-4" />
          <span>{{ formatDownloads(tool.downloads) }}</span>
        </div>
      </div>
      <!-- 分类 -->
      <span class="px-2 py-1 rounded-full bg-gray-100 text-xs font-medium">
        {{ tool.category }}
      </span>
    </div>

    <!-- 免费/付费标识 -->
    <div class="absolute top-3 right-3">
      <span
        v-if="tool.isFree"
        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
      >
        免费
      </span>
      <span
        v-else
        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800"
      >
        付费
      </span>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToolStore } from '@/stores/toolStore.js'
import { IconHeart, IconStarFill, IconDownload } from '@arco-design/web-vue/es/icon'

const props = defineProps({
  tool: {
    type: Object,
    required: true
  }
})

const router = useRouter()
const toolStore = useToolStore()

// 是否已收藏
const isFavorited = computed(() => toolStore.isFavorite(props.tool.id))

// 格式化下载量
const formatDownloads = (downloads) => {
  if (downloads >= 1000000) {
    return `${(downloads / 1000000).toFixed(1)}M`
  } else if (downloads >= 1000) {
    return `${(downloads / 1000).toFixed(1)}K`
  }
  return downloads.toString()
}

// 跳转到详情页
const goToDetail = () => {
  router.push(`/tool/${props.tool.id}`)
}

// 切换收藏状态
const toggleFavorite = () => {
  toolStore.toggleFavorite(props.tool.id)
}
</script>

<style scoped>
.tool-card {
  position: relative;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 卡片悬停效果 */
.tool-card:hover {
  transform: translateY(-2px);
}

/* 收藏按钮动画 */
.text-red-500 {
  animation: heartBeat 0.3s ease-in-out;
}

@keyframes heartBeat {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
</style>
