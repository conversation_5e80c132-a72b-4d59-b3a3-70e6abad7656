<template>
  <div class="home-page">
    <!-- 顶部横幅 -->
    <div class="gradient-bg text-white px-4 py-8 md:py-12">
      <div class="max-w-4xl mx-auto">
        <div class="text-center mb-8">
          <h1 class="text-3xl md:text-4xl font-bold mb-4">
            🛠️ 工具广场
          </h1>
          <p class="text-lg md:text-xl text-purple-100 mb-6">
            发现和分享优质工具的平台
          </p>
          
          <!-- 搜索栏 -->
          <div class="max-w-md mx-auto">
            <SearchBar 
              placeholder="今天想用什么工具？"
              @search="handleSearch"
            />
          </div>
        </div>
        
        <!-- 统计数据 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
          <div class="text-center">
            <div class="text-2xl md:text-3xl font-bold">{{ stats.totalTools }}</div>
            <div class="text-sm text-purple-200">工具总数</div>
          </div>
          <div class="text-center">
            <div class="text-2xl md:text-3xl font-bold">{{ formatNumber(stats.totalDownloads) }}</div>
            <div class="text-sm text-purple-200">总下载量</div>
          </div>
          <div class="text-center">
            <div class="text-2xl md:text-3xl font-bold">{{ stats.totalCategories }}</div>
            <div class="text-sm text-purple-200">工具分类</div>
          </div>
          <div class="text-center">
            <div class="text-2xl md:text-3xl font-bold">{{ stats.averageRating }}</div>
            <div class="text-sm text-purple-200">平均评分</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 py-8">
      <!-- 工具分类 -->
      <section class="mb-12">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900">工具分类</h2>
          <router-link 
            to="/categories"
            class="text-primary hover:text-primary-600 font-medium text-sm flex items-center"
          >
            查看全部
            <icon-right class="w-4 h-4 ml-1" />
          </router-link>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <CategoryCard
            v-for="category in categories.slice(0, 6)"
            :key="category.id"
            :category="category"
          />
        </div>
      </section>

      <!-- 热门工具 -->
      <section class="mb-12">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900">🔥 热门工具</h2>
          <router-link 
            to="/search?sort=popular"
            class="text-primary hover:text-primary-600 font-medium text-sm flex items-center"
          >
            查看更多
            <icon-right class="w-4 h-4 ml-1" />
          </router-link>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ToolCard
            v-for="tool in popularTools"
            :key="tool.id"
            :tool="tool"
          />
        </div>
      </section>

      <!-- 最新工具 -->
      <section class="mb-12">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900">✨ 最新工具</h2>
          <router-link 
            to="/search?sort=latest"
            class="text-primary hover:text-primary-600 font-medium text-sm flex items-center"
          >
            查看更多
            <icon-right class="w-4 h-4 ml-1" />
          </router-link>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ToolCard
            v-for="tool in latestTools"
            :key="tool.id"
            :tool="tool"
          />
        </div>
      </section>

      <!-- 推荐工具 -->
      <section class="mb-12">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-2xl font-bold text-gray-900">⭐ 编辑推荐</h2>
          <router-link 
            to="/search?sort=recommended"
            class="text-primary hover:text-primary-600 font-medium text-sm flex items-center"
          >
            查看更多
            <icon-right class="w-4 h-4 ml-1" />
          </router-link>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ToolCard
            v-for="tool in recommendedTools"
            :key="tool.id"
            :tool="tool"
          />
        </div>
      </section>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useToolStore } from '@/stores/toolStore.js'
import { IconRight } from '@arco-design/web-vue/es/icon'
import SearchBar from '@/components/SearchBar.vue'
import CategoryCard from '@/components/CategoryCard.vue'
import ToolCard from '@/components/ToolCard.vue'

const router = useRouter()
const toolStore = useToolStore()

// 计算属性
const stats = computed(() => toolStore.stats)
const categories = computed(() => toolStore.categories)
const popularTools = computed(() => toolStore.popularTools)
const latestTools = computed(() => toolStore.latestTools)
const recommendedTools = computed(() => toolStore.recommendedTools)

// 格式化数字
const formatNumber = (num) => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 处理搜索
const handleSearch = (keyword) => {
  router.push({
    name: 'search',
    query: { q: keyword }
  })
}

// 页面初始化
onMounted(async () => {
  try {
    await Promise.all([
      toolStore.fetchPopularTools(),
      toolStore.fetchLatestTools(),
      toolStore.fetchRecommendedTools()
    ])
  } catch (error) {
    console.error('首页数据加载失败:', error)
  }
})

// 设置页面名称用于keep-alive
defineOptions({
  name: 'Home'
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #6B46C1 0%, #8B5CF6 50%, #A855F7 100%);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
