<template>
  <div class="home-page">
    <!-- 顶部横幅 -->
    <div class="bg-purple-600 text-white px-4 py-8 md:py-12">
      <div class="max-w-4xl mx-auto">
        <div class="text-center mb-8">
          <h1 class="text-3xl md:text-4xl font-bold mb-4">
            🛠️ 工具广场
          </h1>
          <p class="text-lg md:text-xl mb-6">
            发现和分享优质工具的平台
          </p>
        </div>

        <!-- 统计数据 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
          <div class="text-center">
            <div class="text-2xl md:text-3xl font-bold">6</div>
            <div class="text-sm">工具总数</div>
          </div>
          <div class="text-center">
            <div class="text-2xl md:text-3xl font-bold">1.2M</div>
            <div class="text-sm">总下载量</div>
          </div>
          <div class="text-center">
            <div class="text-2xl md:text-3xl font-bold">6</div>
            <div class="text-sm">工具分类</div>
          </div>
          <div class="text-center">
            <div class="text-2xl md:text-3xl font-bold">4.7</div>
            <div class="text-sm">平均评分</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 py-8">
      <div class="text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">欢迎来到工具广场</h2>
        <p class="text-gray-600">这里是一个工具展示和分享的平台</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置页面名称用于keep-alive
defineOptions({
  name: 'Home'
})
</script>

<style scoped>
.home-page {
  min-height: 100vh;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #6B46C1 0%, #8B5CF6 50%, #A855F7 100%);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
