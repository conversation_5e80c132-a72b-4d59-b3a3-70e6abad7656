{"name": "tool-plaza", "version": "1.0.0", "description": "工具广场 - 工具展示和分享平台移动应用", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@arco-design/web-vue": "^2.55.3", "@vueuse/core": "^10.7.2", "pinia": "^2.1.7", "vue": "^3.4.15", "vue-router": "^4.2.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^9.0.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "postcss": "^8.4.33", "prettier": "^3.2.4", "tailwindcss": "^3.4.1", "vite": "^5.0.11"}}