<template>
  <div class="categories-page min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-4xl mx-auto px-4 py-6">
        <div class="text-center">
          <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            📂 工具分类
          </h1>
          <p class="text-gray-600">
            按分类浏览所有工具，快速找到你需要的
          </p>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 py-8">
      <!-- 分类统计 -->
      <div class="mb-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="bg-white rounded-xl p-4 text-center shadow-sm">
            <div class="text-2xl font-bold text-primary">{{ totalCategories }}</div>
            <div class="text-sm text-gray-600">总分类</div>
          </div>
          <div class="bg-white rounded-xl p-4 text-center shadow-sm">
            <div class="text-2xl font-bold text-green-600">{{ totalTools }}</div>
            <div class="text-sm text-gray-600">总工具</div>
          </div>
          <div class="bg-white rounded-xl p-4 text-center shadow-sm">
            <div class="text-2xl font-bold text-blue-600">{{ averageToolsPerCategory }}</div>
            <div class="text-sm text-gray-600">平均每类</div>
          </div>
          <div class="bg-white rounded-xl p-4 text-center shadow-sm">
            <div class="text-2xl font-bold text-purple-600">{{ mostPopularCategory?.name || '-' }}</div>
            <div class="text-sm text-gray-600">最热分类</div>
          </div>
        </div>
      </div>

      <!-- 分类列表 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <CategoryCard
          v-for="category in categories"
          :key="category.id"
          :category="category"
        />
      </div>

      <!-- 空状态 -->
      <div v-if="categories.length === 0 && !loading" class="text-center py-12">
        <div class="text-6xl mb-4">📂</div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">暂无分类</h3>
        <p class="text-gray-600">分类数据正在准备中...</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-12">
        <a-spin size="large" />
        <div class="mt-4 text-gray-600">正在加载分类...</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useToolStore } from '@/stores/toolStore.js'
import CategoryCard from '@/components/CategoryCard.vue'

const toolStore = useToolStore()

// 计算属性
const categories = computed(() => toolStore.categories)
const loading = computed(() => toolStore.loading)
const stats = computed(() => toolStore.stats)

// 统计数据
const totalCategories = computed(() => categories.value.length)
const totalTools = computed(() => stats.value.totalTools)
const averageToolsPerCategory = computed(() => {
  if (totalCategories.value === 0) return 0
  return Math.round(totalTools.value / totalCategories.value)
})
const mostPopularCategory = computed(() => {
  return categories.value.reduce((prev, current) => {
    return (prev.count > current.count) ? prev : current
  }, categories.value[0])
})

// 页面初始化
onMounted(async () => {
  // 如果分类数据还没有加载，则加载
  if (categories.value.length === 0) {
    await toolStore.fetchCategories()
  }
})

// 设置页面名称用于keep-alive
defineOptions({
  name: 'Categories'
})
</script>

<style scoped>
.categories-page {
  min-height: 100vh;
}
</style>
