<template>
  <div class="categories-page min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-4xl mx-auto px-4 py-6">
        <div class="text-center">
          <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            📂 工具分类
          </h1>
          <p class="text-gray-600">
            按分类浏览所有工具，快速找到你需要的
          </p>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 py-8">
      <div class="text-center py-12">
        <div class="text-6xl mb-4">📂</div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">工具分类</h2>
        <p class="text-gray-600">分类功能正在开发中...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置页面名称用于keep-alive
defineOptions({
  name: 'Categories'
})
</script>

<style scoped>
.categories-page {
  min-height: 100vh;
}
</style>
