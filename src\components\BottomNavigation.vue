<template>
  <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden mobile-safe-area z-40">
    <div class="flex items-center justify-around py-2">
      <router-link
        v-for="item in navItems"
        :key="item.name"
        :to="item.path"
        class="flex flex-col items-center py-2 px-3 min-w-0 flex-1 text-gray-500 hover:text-gray-700"
      >
        <div class="text-xl mb-1">{{ item.icon }}</div>
        <span class="text-xs font-medium truncate">{{ item.label }}</span>
      </router-link>
    </div>
  </div>
</template>

<script setup>
const navItems = [
  {
    name: 'home',
    path: '/',
    icon: '🏠',
    label: '首页'
  },
  {
    name: 'search',
    path: '/search',
    icon: '🔍',
    label: '搜索'
  },
  {
    name: 'categories',
    path: '/categories',
    icon: '📂',
    label: '分类'
  },
  {
    name: 'favorites',
    path: '/favorites',
    icon: '❤️',
    label: '收藏'
  },
  {
    name: 'profile',
    path: '/profile',
    icon: '👤',
    label: '我的'
  }
]
</script>

<style scoped>
/* 底部导航栏样式 */
.router-link-active {
  @apply text-primary;
}

/* 安全区域适配 */
.mobile-safe-area {
  padding-bottom: env(safe-area-inset-bottom);
}

/* 导航项动画 */
.flex.flex-col {
  transition: all 0.2s ease;
}

.flex.flex-col:active {
  transform: scale(0.95);
}
</style>
