<template>
  <div class="search-bar-container">
    <div class="relative">
      <a-input
        v-model="searchKeyword"
        :placeholder="placeholder"
        size="large"
        class="search-input"
        @keyup.enter="handleSearch"
        @input="handleInput"
      >
        <template #prefix>
          <icon-search class="text-gray-400" />
        </template>
        <template #suffix>
          <div class="flex items-center space-x-2">
            <!-- 清除按钮 -->
            <button
              v-if="searchKeyword"
              @click="clearSearch"
              class="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <icon-close class="w-4 h-4" />
            </button>
            <!-- 搜索按钮 -->
            <button
              @click="handleSearch"
              class="bg-primary text-white px-4 py-1 rounded-lg hover:bg-primary-600 transition-colors text-sm font-medium"
              :disabled="!searchKeyword.trim()"
            >
              搜索
            </button>
          </div>
        </template>
      </a-input>
      
      <!-- 搜索建议下拉框 -->
      <div
        v-if="showSuggestions && suggestions.length > 0"
        class="absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-lg border border-gray-200 z-50 max-h-64 overflow-y-auto"
      >
        <div class="p-2">
          <div class="text-xs text-gray-500 px-3 py-2 font-medium">搜索建议</div>
          <button
            v-for="suggestion in suggestions"
            :key="suggestion"
            @click="selectSuggestion(suggestion)"
            class="w-full text-left px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors text-sm"
          >
            <icon-search class="w-4 h-4 text-gray-400 mr-2 inline" />
            {{ suggestion }}
          </button>
        </div>
      </div>
    </div>
    
    <!-- 热门搜索标签 -->
    <div v-if="showHotTags && hotTags.length > 0" class="mt-4">
      <div class="text-sm text-gray-600 mb-2 font-medium">热门搜索</div>
      <div class="flex flex-wrap gap-2">
        <button
          v-for="tag in hotTags"
          :key="tag"
          @click="selectSuggestion(tag)"
          class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-primary-100 hover:text-primary-700 transition-colors"
        >
          {{ tag }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { IconSearch, IconClose } from '@arco-design/web-vue/es/icon'

const props = defineProps({
  placeholder: {
    type: String,
    default: '搜索工具...'
  },
  showHotTags: {
    type: Boolean,
    default: false
  },
  autoFocus: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['search', 'input'])

const router = useRouter()

const searchKeyword = ref('')
const showSuggestions = ref(false)

// 搜索建议
const suggestions = computed(() => {
  if (!searchKeyword.value.trim()) return []
  
  const keyword = searchKeyword.value.toLowerCase()
  const allSuggestions = [
    'Figma', 'Notion', 'VS Code', 'Typora', 'Anki', 'Spotify',
    '设计工具', '效率工具', '开发工具', '写作工具', '学习工具', '娱乐工具',
    '免费工具', '付费工具', '协作工具', '原型设计', '代码编辑', '笔记应用'
  ]
  
  return allSuggestions
    .filter(item => item.toLowerCase().includes(keyword))
    .slice(0, 6)
})

// 热门搜索标签
const hotTags = ref([
  'Figma', 'Notion', 'VS Code', '设计工具', '效率工具', '免费工具'
])

// 处理搜索
const handleSearch = () => {
  if (!searchKeyword.value.trim()) return
  
  showSuggestions.value = false
  emit('search', searchKeyword.value.trim())
  
  // 如果不在搜索页面，跳转到搜索页面
  if (router.currentRoute.value.name !== 'search') {
    router.push({
      name: 'search',
      query: { q: searchKeyword.value.trim() }
    })
  }
}

// 处理输入
const handleInput = (value) => {
  showSuggestions.value = value.length > 0
  emit('input', value)
}

// 选择搜索建议
const selectSuggestion = (suggestion) => {
  searchKeyword.value = suggestion
  handleSearch()
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  showSuggestions.value = false
  emit('input', '')
}

// 点击外部关闭建议框
const handleClickOutside = (event) => {
  if (!event.target.closest('.search-bar-container')) {
    showSuggestions.value = false
  }
}

// 监听搜索关键词变化
watch(searchKeyword, (newValue) => {
  showSuggestions.value = newValue.length > 0
})

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.search-input {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.search-input:focus-within {
  border-color: #6B46C1;
  box-shadow: 0 0 0 3px rgba(107, 70, 193, 0.1);
}

/* 输入框内部样式调整 */
:deep(.arco-input) {
  border: none;
  box-shadow: none;
}

:deep(.arco-input:focus) {
  border: none;
  box-shadow: none;
}
</style>
