import { tools, categories, popularTools, latestTools, recommendedTools, stats } from '@/data/mockData.js'

// 模拟API延迟
const delay = (ms = 300) => new Promise(resolve => setTimeout(resolve, ms))

export const toolService = {
  // 获取所有工具
  async getAllTools() {
    await delay()
    return {
      success: true,
      data: tools,
      total: tools.length
    }
  },

  // 根据ID获取工具详情
  async getToolById(id) {
    await delay()
    const tool = tools.find(t => t.id === parseInt(id))
    return {
      success: !!tool,
      data: tool,
      message: tool ? '获取成功' : '工具不存在'
    }
  },

  // 搜索工具
  async searchTools(keyword, categoryId = null, page = 1, pageSize = 10) {
    await delay()
    let filteredTools = tools

    // 按关键词搜索
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase()
      filteredTools = filteredTools.filter(tool => 
        tool.name.toLowerCase().includes(lowerKeyword) ||
        tool.description.toLowerCase().includes(lowerKeyword) ||
        tool.tags.some(tag => tag.toLowerCase().includes(lowerKeyword))
      )
    }

    // 按分类筛选
    if (categoryId) {
      filteredTools = filteredTools.filter(tool => tool.categoryId === parseInt(categoryId))
    }

    // 分页
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const paginatedTools = filteredTools.slice(start, end)

    return {
      success: true,
      data: paginatedTools,
      total: filteredTools.length,
      page,
      pageSize,
      totalPages: Math.ceil(filteredTools.length / pageSize)
    }
  },

  // 获取分类列表
  async getCategories() {
    await delay()
    return {
      success: true,
      data: categories
    }
  },

  // 根据分类ID获取工具
  async getToolsByCategory(categoryId, page = 1, pageSize = 10) {
    await delay()
    const filteredTools = tools.filter(tool => tool.categoryId === parseInt(categoryId))
    
    const start = (page - 1) * pageSize
    const end = start + pageSize
    const paginatedTools = filteredTools.slice(start, end)

    return {
      success: true,
      data: paginatedTools,
      total: filteredTools.length,
      page,
      pageSize,
      totalPages: Math.ceil(filteredTools.length / pageSize)
    }
  },

  // 获取热门工具
  async getPopularTools() {
    await delay()
    return {
      success: true,
      data: popularTools
    }
  },

  // 获取最新工具
  async getLatestTools() {
    await delay()
    return {
      success: true,
      data: latestTools
    }
  },

  // 获取推荐工具
  async getRecommendedTools() {
    await delay()
    return {
      success: true,
      data: recommendedTools
    }
  },

  // 获取统计数据
  async getStats() {
    await delay()
    return {
      success: true,
      data: stats
    }
  },

  // 获取相关工具推荐
  async getRelatedTools(toolId, limit = 4) {
    await delay()
    const currentTool = tools.find(t => t.id === parseInt(toolId))
    if (!currentTool) {
      return { success: false, data: [], message: '工具不存在' }
    }

    // 基于分类和标签推荐相关工具
    const relatedTools = tools
      .filter(tool => tool.id !== parseInt(toolId))
      .filter(tool => 
        tool.categoryId === currentTool.categoryId ||
        tool.tags.some(tag => currentTool.tags.includes(tag))
      )
      .sort((a, b) => b.rating - a.rating)
      .slice(0, limit)

    return {
      success: true,
      data: relatedTools
    }
  }
}
