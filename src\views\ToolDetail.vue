<template>
  <div class="tool-detail-page min-h-screen bg-gray-50">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center min-h-screen">
      <a-spin size="large" />
    </div>

    <!-- 工具详情内容 -->
    <div v-else-if="currentTool" class="max-w-4xl mx-auto">
      <!-- 返回按钮 -->
      <div class="px-4 py-4">
        <button
          @click="goBack"
          class="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
        >
          <icon-left class="w-5 h-5 mr-2" />
          返回
        </button>
      </div>

      <!-- 工具头部信息 -->
      <div class="bg-white mx-4 rounded-2xl shadow-sm p-6 mb-6">
        <div class="flex items-start space-x-4">
          <!-- 工具图标 -->
          <div class="flex-shrink-0">
            <div class="w-20 h-20 rounded-2xl bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white text-3xl shadow-lg">
              {{ currentTool.icon }}
            </div>
          </div>
          
          <!-- 基本信息 -->
          <div class="flex-1 min-w-0">
            <div class="flex items-start justify-between">
              <div>
                <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                  {{ currentTool.name }}
                </h1>
                <p class="text-gray-600 text-lg mb-4">
                  {{ currentTool.description }}
                </p>
                
                <!-- 评分和统计 -->
                <div class="flex items-center space-x-6 text-sm">
                  <div class="flex items-center space-x-1">
                    <icon-star-fill class="w-5 h-5 text-yellow-400" />
                    <span class="font-semibold">{{ currentTool.rating }}</span>
                    <span class="text-gray-500">评分</span>
                  </div>
                  <div class="flex items-center space-x-1">
                    <icon-download class="w-5 h-5 text-gray-400" />
                    <span class="font-semibold">{{ formatDownloads(currentTool.downloads) }}</span>
                    <span class="text-gray-500">下载</span>
                  </div>
                  <div class="flex items-center space-x-1">
                    <span class="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span class="text-gray-500">{{ currentTool.lastUpdate }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 收藏按钮 -->
              <button
                @click="toggleFavorite"
                class="flex items-center space-x-2 px-4 py-2 rounded-xl transition-all"
                :class="[
                  isFavorited 
                    ? 'bg-red-50 text-red-600 hover:bg-red-100' 
                    : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
                ]"
              >
                <icon-heart :fill="isFavorited" class="w-5 h-5" />
                <span>{{ isFavorited ? '已收藏' : '收藏' }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 标签 -->
        <div class="flex flex-wrap gap-2 mt-6">
          <span
            v-for="tag in currentTool.tags"
            :key="tag"
            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-secondary-100 text-primary-700"
          >
            {{ tag }}
          </span>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-wrap gap-3 mt-6">
          <a
            :href="currentTool.website"
            target="_blank"
            class="flex items-center space-x-2 px-6 py-3 bg-primary text-white rounded-xl hover:bg-primary-600 transition-colors font-medium"
          >
            <icon-link class="w-5 h-5" />
            <span>访问官网</span>
          </a>
          <button class="flex items-center space-x-2 px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors font-medium">
            <icon-share-alt class="w-5 h-5" />
            <span>分享</span>
          </button>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 px-4 mb-6">
        <!-- 主要内容 -->
        <div class="lg:col-span-2 space-y-6">
          <!-- 功能特性 -->
          <div class="bg-white rounded-2xl shadow-sm p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-4">✨ 主要功能</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div
                v-for="feature in currentTool.features"
                :key="feature"
                class="flex items-center space-x-3 p-3 bg-gray-50 rounded-xl"
              >
                <div class="w-2 h-2 bg-primary rounded-full"></div>
                <span class="text-gray-700">{{ feature }}</span>
              </div>
            </div>
          </div>

          <!-- 截图展示 -->
          <div class="bg-white rounded-2xl shadow-sm p-6">
            <h2 class="text-xl font-bold text-gray-900 mb-4">📸 应用截图</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div
                v-for="(screenshot, index) in currentTool.screenshots"
                :key="index"
                class="aspect-video bg-gray-100 rounded-xl flex items-center justify-center"
              >
                <div class="text-gray-400 text-center">
                  <div class="text-4xl mb-2">🖼️</div>
                  <div class="text-sm">截图 {{ index + 1 }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 侧边栏信息 -->
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div class="bg-white rounded-2xl shadow-sm p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">📋 基本信息</h3>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span class="text-gray-600">开发者</span>
                <span class="font-medium">{{ currentTool.developer }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">版本</span>
                <span class="font-medium">{{ currentTool.version }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">大小</span>
                <span class="font-medium">{{ currentTool.size }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">分类</span>
                <span class="font-medium">{{ currentTool.category }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">价格</span>
                <span class="font-medium" :class="currentTool.isFree ? 'text-green-600' : 'text-orange-600'">
                  {{ currentTool.isFree ? '免费' : '付费' }}
                </span>
              </div>
            </div>
          </div>

          <!-- 相关工具推荐 -->
          <div v-if="relatedTools.length > 0" class="bg-white rounded-2xl shadow-sm p-6">
            <h3 class="text-lg font-bold text-gray-900 mb-4">🔗 相关工具</h3>
            <div class="space-y-3">
              <div
                v-for="tool in relatedTools"
                :key="tool.id"
                @click="goToTool(tool.id)"
                class="flex items-center space-x-3 p-3 rounded-xl hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white text-lg">
                  {{ tool.icon }}
                </div>
                <div class="flex-1 min-w-0">
                  <div class="font-medium text-gray-900 truncate">{{ tool.name }}</div>
                  <div class="text-sm text-gray-500 flex items-center">
                    <icon-star-fill class="w-3 h-3 text-yellow-400 mr-1" />
                    {{ tool.rating }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 工具不存在 -->
    <div v-else class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="text-6xl mb-4">😵</div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">工具不存在</h2>
        <p class="text-gray-600 mb-4">您访问的工具可能已被删除或不存在</p>
        <router-link
          to="/"
          class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
        >
          返回首页
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToolStore } from '@/stores/toolStore.js'
import { 
  IconLeft, IconStarFill, IconDownload, IconHeart, 
  IconLink, IconShareAlt 
} from '@arco-design/web-vue/es/icon'

const route = useRoute()
const router = useRouter()
const toolStore = useToolStore()

// 计算属性
const currentTool = computed(() => toolStore.currentTool)
const loading = computed(() => toolStore.loading)
const relatedTools = computed(() => toolStore.relatedTools)
const isFavorited = computed(() => {
  return currentTool.value ? toolStore.isFavorite(currentTool.value.id) : false
})

// 格式化下载量
const formatDownloads = (downloads) => {
  if (downloads >= 1000000) {
    return `${(downloads / 1000000).toFixed(1)}M`
  } else if (downloads >= 1000) {
    return `${(downloads / 1000).toFixed(1)}K`
  }
  return downloads.toString()
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 跳转到其他工具
const goToTool = (toolId) => {
  router.push(`/tool/${toolId}`)
}

// 切换收藏状态
const toggleFavorite = () => {
  if (currentTool.value) {
    toolStore.toggleFavorite(currentTool.value.id)
  }
}

// 加载工具详情
const loadToolDetail = async (toolId) => {
  await toolStore.fetchToolDetail(toolId)
  if (currentTool.value) {
    await toolStore.fetchRelatedTools(toolId)
  }
}

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    loadToolDetail(newId)
  }
}, { immediate: true })

// 页面初始化
onMounted(() => {
  const toolId = route.params.id
  if (toolId) {
    loadToolDetail(toolId)
  }
})
</script>

<style scoped>
.tool-detail-page {
  min-height: 100vh;
}
</style>
