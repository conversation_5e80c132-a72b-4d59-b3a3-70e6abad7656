<template>
  <div 
    class="category-card cursor-pointer group"
    @click="goToCategory"
    :style="{ '--category-color': category.color }"
  >
    <div class="flex items-center space-x-4">
      <!-- 分类图标 -->
      <div class="flex-shrink-0">
        <div 
          class="w-14 h-14 rounded-2xl flex items-center justify-center text-white text-2xl shadow-lg category-icon"
          :style="{ backgroundColor: category.color }"
        >
          {{ category.icon }}
        </div>
      </div>
      
      <!-- 分类信息 -->
      <div class="flex-1 min-w-0">
        <h3 class="font-semibold text-gray-900 text-lg mb-1 group-hover:text-primary transition-colors">
          {{ category.name }}
        </h3>
        <p class="text-gray-500 text-sm">
          {{ category.count }} 个工具
        </p>
      </div>
      
      <!-- 箭头图标 -->
      <div class="flex-shrink-0 text-gray-400 group-hover:text-primary transition-colors">
        <icon-right class="w-5 h-5" />
      </div>
    </div>
    
    <!-- 装饰性背景 -->
    <div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-5 transition-opacity category-bg"></div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { IconRight } from '@arco-design/web-vue/es/icon'

const props = defineProps({
  category: {
    type: Object,
    required: true
  }
})

const router = useRouter()

// 跳转到分类详情页
const goToCategory = () => {
  router.push(`/category/${props.category.id}`)
}
</script>

<style scoped>
.category-card {
  @apply bg-white rounded-2xl shadow-sm border border-gray-100 p-4 transition-all duration-300 hover:shadow-lg hover:scale-105;
  position: relative;
  overflow: hidden;
}

.category-icon {
  transition: all 0.3s ease;
}

.group:hover .category-icon {
  transform: scale(1.1);
}

.category-bg {
  background: var(--category-color);
}

/* 卡片悬停效果 */
.category-card:hover {
  transform: translateY(-2px);
}
</style>
