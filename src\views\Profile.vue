<template>
  <div class="profile-page min-h-screen bg-gray-50">
    <!-- 用户信息头部 -->
    <div class="gradient-bg text-white px-4 py-8">
      <div class="max-w-4xl mx-auto text-center">
        <!-- 用户头像 -->
        <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
          👤
        </div>
        <h1 class="text-2xl font-bold mb-2">工具探索者</h1>
        <p class="text-purple-200">发现优质工具，提升工作效率</p>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 py-8">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div class="bg-white rounded-xl p-4 text-center shadow-sm">
          <div class="text-2xl font-bold text-primary">{{ favoriteCount }}</div>
          <div class="text-sm text-gray-600">收藏工具</div>
        </div>
        <div class="bg-white rounded-xl p-4 text-center shadow-sm">
          <div class="text-2xl font-bold text-green-600">{{ historyCount }}</div>
          <div class="text-sm text-gray-600">浏览历史</div>
        </div>
        <div class="bg-white rounded-xl p-4 text-center shadow-sm">
          <div class="text-2xl font-bold text-blue-600">{{ categoriesUsed }}</div>
          <div class="text-sm text-gray-600">涉及分类</div>
        </div>
        <div class="bg-white rounded-xl p-4 text-center shadow-sm">
          <div class="text-2xl font-bold text-purple-600">{{ daysActive }}</div>
          <div class="text-sm text-gray-600">活跃天数</div>
        </div>
      </div>

      <!-- 功能菜单 -->
      <div class="space-y-4 mb-8">
        <!-- 浏览历史 -->
        <div class="bg-white rounded-xl shadow-sm">
          <div class="p-4 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
              <icon-history class="w-5 h-5 mr-2 text-blue-500" />
              最近浏览
            </h3>
          </div>
          <div v-if="historyTools.length > 0" class="p-4">
            <div class="space-y-3">
              <div
                v-for="tool in historyTools.slice(0, 5)"
                :key="tool.id"
                @click="goToTool(tool.id)"
                class="flex items-center space-x-3 p-3 rounded-xl hover:bg-gray-50 cursor-pointer transition-colors"
              >
                <div class="w-10 h-10 rounded-lg bg-gradient-to-br from-primary-400 to-primary-600 flex items-center justify-center text-white text-lg">
                  {{ tool.icon }}
                </div>
                <div class="flex-1 min-w-0">
                  <div class="font-medium text-gray-900 truncate">{{ tool.name }}</div>
                  <div class="text-sm text-gray-500">{{ tool.category }}</div>
                </div>
                <div class="text-sm text-gray-400">
                  <icon-right class="w-4 h-4" />
                </div>
              </div>
            </div>
            <div v-if="historyTools.length > 5" class="mt-4 text-center">
              <button class="text-primary hover:text-primary-600 text-sm font-medium">
                查看全部历史
              </button>
            </div>
          </div>
          <div v-else class="p-8 text-center text-gray-500">
            <div class="text-4xl mb-2">📱</div>
            <div class="text-sm">暂无浏览历史</div>
          </div>
        </div>

        <!-- 设置选项 -->
        <div class="bg-white rounded-xl shadow-sm">
          <div class="p-4 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
              <icon-settings class="w-5 h-5 mr-2 text-gray-500" />
              设置
            </h3>
          </div>
          <div class="p-4 space-y-3">
            <div class="flex items-center justify-between py-2">
              <div class="flex items-center space-x-3">
                <icon-notification class="w-5 h-5 text-gray-400" />
                <span class="text-gray-700">推送通知</span>
              </div>
              <a-switch v-model="settings.notifications" />
            </div>
            <div class="flex items-center justify-between py-2">
              <div class="flex items-center space-x-3">
                <icon-moon class="w-5 h-5 text-gray-400" />
                <span class="text-gray-700">深色模式</span>
              </div>
              <a-switch v-model="settings.darkMode" />
            </div>
            <div class="flex items-center justify-between py-2">
              <div class="flex items-center space-x-3">
                <icon-safe class="w-5 h-5 text-gray-400" />
                <span class="text-gray-700">隐私保护</span>
              </div>
              <a-switch v-model="settings.privacy" />
            </div>
          </div>
        </div>

        <!-- 其他功能 -->
        <div class="bg-white rounded-xl shadow-sm">
          <div class="p-4 space-y-3">
            <button class="w-full flex items-center justify-between py-3 text-left hover:bg-gray-50 rounded-lg px-3 transition-colors">
              <div class="flex items-center space-x-3">
                <icon-question-circle class="w-5 h-5 text-gray-400" />
                <span class="text-gray-700">帮助与反馈</span>
              </div>
              <icon-right class="w-4 h-4 text-gray-400" />
            </button>
            <button class="w-full flex items-center justify-between py-3 text-left hover:bg-gray-50 rounded-lg px-3 transition-colors">
              <div class="flex items-center space-x-3">
                <icon-info-circle class="w-5 h-5 text-gray-400" />
                <span class="text-gray-700">关于我们</span>
              </div>
              <icon-right class="w-4 h-4 text-gray-400" />
            </button>
            <button 
              @click="clearData"
              class="w-full flex items-center justify-between py-3 text-left hover:bg-gray-50 rounded-lg px-3 transition-colors"
            >
              <div class="flex items-center space-x-3">
                <icon-delete class="w-5 h-5 text-red-400" />
                <span class="text-red-600">清除数据</span>
              </div>
              <icon-right class="w-4 h-4 text-gray-400" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToolStore } from '@/stores/toolStore.js'
import { 
  IconHistory, IconSettings, IconNotification, IconMoon, 
  IconSafe, IconQuestionCircle, IconInfoCircle, IconDelete, IconRight 
} from '@arco-design/web-vue/es/icon'

const router = useRouter()
const toolStore = useToolStore()

// 设置数据
const settings = ref({
  notifications: true,
  darkMode: false,
  privacy: true
})

// 计算属性
const favoriteCount = computed(() => toolStore.favorites.length)
const historyCount = computed(() => toolStore.viewHistory.length)
const historyTools = computed(() => toolStore.historyTools)

const categoriesUsed = computed(() => {
  const categories = new Set()
  toolStore.favoriteTools.forEach(tool => {
    categories.add(tool.category)
  })
  toolStore.historyTools.forEach(tool => {
    categories.add(tool.category)
  })
  return categories.size
})

const daysActive = computed(() => {
  // 模拟活跃天数
  return Math.max(1, favoriteCount.value + Math.floor(historyCount.value / 3))
})

// 跳转到工具详情
const goToTool = (toolId) => {
  router.push(`/tool/${toolId}`)
}

// 清除数据
const clearData = () => {
  // 这里可以添加确认对话框
  localStorage.removeItem('tool-favorites')
  localStorage.removeItem('tool-history')
  toolStore.favorites.length = 0
  toolStore.viewHistory.length = 0
  
  // 显示成功提示
  console.log('数据已清除')
}

// 设置页面名称用于keep-alive
defineOptions({
  name: 'Profile'
})
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #6B46C1 0%, #8B5CF6 50%, #A855F7 100%);
}
</style>
