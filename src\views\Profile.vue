<template>
  <div class="profile-page min-h-screen bg-gray-50">
    <!-- 用户信息头部 -->
    <div class="bg-purple-600 text-white px-4 py-8">
      <div class="max-w-4xl mx-auto text-center">
        <!-- 用户头像 -->
        <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
          👤
        </div>
        <h1 class="text-2xl font-bold mb-2">工具探索者</h1>
        <p class="text-purple-200">发现优质工具，提升工作效率</p>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 py-8">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <div class="bg-white rounded-xl p-4 text-center shadow-sm">
          <div class="text-2xl font-bold text-purple-600">0</div>
          <div class="text-sm text-gray-600">收藏工具</div>
        </div>
        <div class="bg-white rounded-xl p-4 text-center shadow-sm">
          <div class="text-2xl font-bold text-green-600">0</div>
          <div class="text-sm text-gray-600">浏览历史</div>
        </div>
        <div class="bg-white rounded-xl p-4 text-center shadow-sm">
          <div class="text-2xl font-bold text-blue-600">0</div>
          <div class="text-sm text-gray-600">涉及分类</div>
        </div>
        <div class="bg-white rounded-xl p-4 text-center shadow-sm">
          <div class="text-2xl font-bold text-purple-600">1</div>
          <div class="text-sm text-gray-600">活跃天数</div>
        </div>
      </div>

      <!-- 功能菜单 -->
      <div class="space-y-4 mb-8">
        <!-- 浏览历史 -->
        <div class="bg-white rounded-xl shadow-sm">
          <div class="p-4 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
              📱 最近浏览
            </h3>
          </div>
          <div class="p-8 text-center text-gray-500">
            <div class="text-4xl mb-2">📱</div>
            <div class="text-sm">暂无浏览历史</div>
          </div>
        </div>

        <!-- 设置选项 -->
        <div class="bg-white rounded-xl shadow-sm">
          <div class="p-4 border-b border-gray-100">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
              ⚙️ 设置
            </h3>
          </div>
          <div class="p-4 space-y-3">
            <div class="flex items-center justify-between py-2">
              <span class="text-gray-700">推送通知</span>
              <div class="w-12 h-6 bg-gray-200 rounded-full"></div>
            </div>
            <div class="flex items-center justify-between py-2">
              <span class="text-gray-700">深色模式</span>
              <div class="w-12 h-6 bg-gray-200 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置页面名称用于keep-alive
defineOptions({
  name: 'Profile'
})
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #6B46C1 0%, #8B5CF6 50%, #A855F7 100%);
}
</style>
