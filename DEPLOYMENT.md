# 部署指南

本文档介绍如何部署工具广场应用到不同的平台。

## 📋 部署前准备

### 环境要求
- Node.js 16.0+
- npm 7.0+ 或 yarn 1.22+

### 构建生产版本
```bash
npm run build
```

构建完成后，会在 `dist` 目录生成生产版本文件。

## 🌐 静态网站部署

### Vercel 部署

1. 将项目推送到 GitHub
2. 访问 [Vercel](https://vercel.com/)
3. 导入 GitHub 项目
4. 配置构建设置：
   - Build Command: `npm run build`
   - Output Directory: `dist`
5. 点击部署

### Netlify 部署

1. 将项目推送到 GitHub
2. 访问 [Netlify](https://netlify.com/)
3. 连接 GitHub 仓库
4. 配置构建设置：
   - Build command: `npm run build`
   - Publish directory: `dist`
5. 部署网站

### GitHub Pages 部署

1. 安装 gh-pages 包：
```bash
npm install --save-dev gh-pages
```

2. 在 `package.json` 中添加部署脚本：
```json
{
  "scripts": {
    "deploy": "gh-pages -d dist"
  },
  "homepage": "https://yourusername.github.io/tool-plaza"
}
```

3. 构建并部署：
```bash
npm run build
npm run deploy
```

## 🐳 Docker 部署

### 创建 Dockerfile
```dockerfile
# 构建阶段
FROM node:16-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:stable-alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### 创建 nginx.conf
```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

### 构建和运行 Docker 容器
```bash
# 构建镜像
docker build -t tool-plaza .

# 运行容器
docker run -d -p 80:80 tool-plaza
```

## ☁️ 云服务器部署

### 使用 PM2 部署

1. 安装 PM2：
```bash
npm install -g pm2
```

2. 创建 ecosystem.config.js：
```javascript
module.exports = {
  apps: [{
    name: 'tool-plaza',
    script: 'serve',
    args: '-s dist -l 3000',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production'
    }
  }]
}
```

3. 安装 serve 并启动：
```bash
npm install -g serve
pm2 start ecosystem.config.js
```

## 📱 移动应用打包

### 使用 Cordova

1. 安装 Cordova：
```bash
npm install -g cordova
```

2. 创建 Cordova 项目：
```bash
cordova create tool-plaza-mobile com.example.toolplaza "Tool Plaza"
cd tool-plaza-mobile
```

3. 添加平台：
```bash
cordova platform add android
cordova platform add ios
```

4. 复制构建文件到 www 目录
5. 构建应用：
```bash
cordova build android
cordova build ios
```

### 使用 Capacitor

1. 安装 Capacitor：
```bash
npm install @capacitor/core @capacitor/cli
```

2. 初始化 Capacitor：
```bash
npx cap init
```

3. 添加平台：
```bash
npx cap add android
npx cap add ios
```

4. 构建并同步：
```bash
npm run build
npx cap sync
```

5. 打开原生 IDE：
```bash
npx cap open android
npx cap open ios
```

## 🔧 配置优化

### 生产环境优化

1. 启用 gzip 压缩
2. 配置 CDN
3. 设置适当的缓存策略
4. 启用 HTTPS

### 性能监控

建议集成以下工具：
- Google Analytics
- Sentry (错误监控)
- Lighthouse (性能分析)

## 🚀 自动化部署

### GitHub Actions 示例

创建 `.github/workflows/deploy.yml`：

```yaml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '16'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build
      run: npm run build
      
    - name: Deploy to Vercel
      uses: vercel/action@v1
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## 📝 注意事项

1. **路由配置**: 确保服务器配置支持 SPA 路由
2. **环境变量**: 生产环境中设置正确的环境变量
3. **API 地址**: 更新 API 地址为生产环境地址
4. **安全性**: 启用 HTTPS，配置安全头
5. **监控**: 设置错误监控和性能监控

## 🆘 故障排除

### 常见问题

1. **白屏问题**: 检查路由配置和资源路径
2. **404 错误**: 确保服务器支持 SPA 路由
3. **资源加载失败**: 检查 base URL 配置
4. **移动端适配**: 测试不同设备和浏览器

### 调试工具

- Chrome DevTools
- Vue DevTools
- Network 面板
- Console 日志

---

如有部署问题，请查看项目文档或提交 Issue。
