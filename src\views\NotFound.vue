<template>
  <div class="not-found-page min-h-screen bg-gray-50 flex items-center justify-center">
    <div class="text-center px-4">
      <!-- 404 图标 -->
      <div class="text-8xl mb-6">🤔</div>
      
      <!-- 错误信息 -->
      <h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-4">404</h1>
      <h2 class="text-xl md:text-2xl font-semibold text-gray-700 mb-4">页面不存在</h2>
      <p class="text-gray-600 mb-8 max-w-md mx-auto">
        抱歉，您访问的页面可能已被删除、重命名或暂时不可用
      </p>
      
      <!-- 操作按钮 -->
      <div class="space-y-4">
        <router-link
          to="/"
          class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-xl hover:bg-primary-600 transition-colors font-medium"
        >
          <icon-home class="w-5 h-5 mr-2" />
          返回首页
        </router-link>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link
            to="/search"
            class="inline-flex items-center px-4 py-2 text-primary hover:text-primary-600 transition-colors font-medium"
          >
            <icon-search class="w-5 h-5 mr-2" />
            搜索工具
          </router-link>
          
          <router-link
            to="/categories"
            class="inline-flex items-center px-4 py-2 text-primary hover:text-primary-600 transition-colors font-medium"
          >
            <icon-apps class="w-5 h-5 mr-2" />
            浏览分类
          </router-link>
        </div>
      </div>
      
      <!-- 推荐内容 -->
      <div class="mt-12 max-w-2xl mx-auto">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">或许您想要：</h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div class="bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow">
            <div class="text-2xl mb-2">🔥</div>
            <h4 class="font-medium text-gray-900 mb-1">热门工具</h4>
            <p class="text-sm text-gray-600">查看最受欢迎的工具</p>
          </div>
          <div class="bg-white rounded-xl p-4 shadow-sm hover:shadow-md transition-shadow">
            <div class="text-2xl mb-2">✨</div>
            <h4 class="font-medium text-gray-900 mb-1">最新工具</h4>
            <p class="text-sm text-gray-600">发现最新上架的工具</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { IconHome, IconSearch, IconApps } from '@arco-design/web-vue/es/icon'
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
}
</style>
