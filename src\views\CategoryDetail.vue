<template>
  <div class="category-detail-page min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-4xl mx-auto px-4 py-4">
        <!-- 返回按钮 -->
        <button
          @click="goBack"
          class="flex items-center text-gray-600 hover:text-gray-900 transition-colors mb-4"
        >
          <icon-left class="w-5 h-5 mr-2" />
          返回分类
        </button>

        <!-- 分类信息 -->
        <div v-if="currentCategory" class="flex items-center space-x-4">
          <div 
            class="w-16 h-16 rounded-2xl flex items-center justify-center text-white text-2xl shadow-lg"
            :style="{ backgroundColor: currentCategory.color }"
          >
            {{ currentCategory.icon }}
          </div>
          <div>
            <h1 class="text-2xl md:text-3xl font-bold text-gray-900">
              {{ currentCategory.name }}
            </h1>
            <p class="text-gray-600 mt-1">
              共 {{ pagination.total }} 个工具
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 py-8">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-12">
        <a-spin size="large" />
        <div class="mt-4 text-gray-600">正在加载工具...</div>
      </div>

      <!-- 工具列表 -->
      <div v-else-if="tools.length > 0">
        <!-- 排序和筛选 -->
        <div class="flex items-center justify-between mb-6">
          <div class="text-sm text-gray-600">
            显示 {{ tools.length }} 个工具，共 {{ pagination.total }} 个
          </div>
          <a-select
            v-model="sortBy"
            :style="{ width: '120px' }"
            @change="handleSortChange"
          >
            <a-option value="rating">评分</a-option>
            <a-option value="downloads">下载量</a-option>
            <a-option value="latest">最新</a-option>
            <a-option value="name">名称</a-option>
          </a-select>
        </div>

        <!-- 工具网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <ToolCard
            v-for="tool in sortedTools"
            :key="tool.id"
            :tool="tool"
          />
        </div>

        <!-- 分页 -->
        <div v-if="pagination.totalPages > 1" class="flex justify-center">
          <a-pagination
            v-model:current="pagination.page"
            :total="pagination.total"
            :page-size="pagination.pageSize"
            :show-total="true"
            :show-jumper="true"
            @change="handlePageChange"
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12">
        <div class="text-6xl mb-4">📂</div>
        <h3 class="text-lg font-semibold text-gray-900 mb-2">该分类暂无工具</h3>
        <p class="text-gray-600 mb-4">该分类下还没有工具，去看看其他分类吧</p>
        <router-link
          to="/categories"
          class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
        >
          浏览其他分类
          <icon-right class="w-4 h-4 ml-2" />
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToolStore } from '@/stores/toolStore.js'
import { IconLeft, IconRight } from '@arco-design/web-vue/es/icon'
import ToolCard from '@/components/ToolCard.vue'

const route = useRoute()
const router = useRouter()
const toolStore = useToolStore()

// 响应式数据
const sortBy = ref('rating')

// 计算属性
const currentCategory = computed(() => toolStore.currentCategory)
const tools = computed(() => toolStore.tools)
const loading = computed(() => toolStore.loading)
const pagination = computed(() => toolStore.pagination)

// 排序后的工具列表
const sortedTools = computed(() => {
  const toolsList = [...tools.value]
  
  switch (sortBy.value) {
    case 'rating':
      return toolsList.sort((a, b) => b.rating - a.rating)
    case 'downloads':
      return toolsList.sort((a, b) => b.downloads - a.downloads)
    case 'latest':
      return toolsList.sort((a, b) => new Date(b.lastUpdate) - new Date(a.lastUpdate))
    case 'name':
      return toolsList.sort((a, b) => a.name.localeCompare(b.name))
    default:
      return toolsList
  }
})

// 返回上一页
const goBack = () => {
  router.back()
}

// 处理排序变化
const handleSortChange = () => {
  // 排序在计算属性中处理，这里不需要额外操作
}

// 处理分页变化
const handlePageChange = (page) => {
  const categoryId = route.params.id
  if (categoryId) {
    toolStore.fetchToolsByCategory(categoryId, page)
  }
}

// 加载分类工具
const loadCategoryTools = async (categoryId) => {
  await toolStore.fetchToolsByCategory(categoryId, 1)
}

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    loadCategoryTools(newId)
  }
}, { immediate: true })

// 页面初始化
onMounted(() => {
  const categoryId = route.params.id
  if (categoryId) {
    loadCategoryTools(categoryId)
  }
})
</script>

<style scoped>
.category-detail-page {
  min-height: 100vh;
}
</style>
