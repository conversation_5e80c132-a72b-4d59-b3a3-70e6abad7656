# 工具广场 - Tool Plaza

一个现代化的工具展示和分享平台移动应用，基于 Vue 3 + Vite + TailwindCSS + ArcoDesign 构建。

## ✨ 特性

- 🎨 **现代化设计**: 采用紫色主题，美观的UI界面
- 📱 **移动端优先**: 响应式设计，完美适配各种屏幕尺寸
- 🔍 **智能搜索**: 支持关键词搜索、分类筛选、排序功能
- ❤️ **收藏功能**: 本地存储收藏的工具，支持收藏管理
- 📊 **数据统计**: 丰富的统计信息和数据展示
- 🚀 **性能优化**: 路由懒加载、组件缓存、优化的构建配置

## 🛠️ 技术栈

- **前端框架**: Vue 3.4+ (Composition API)
- **构建工具**: Vite 5.0+
- **UI框架**: ArcoDesign Vue
- **CSS框架**: TailwindCSS 3.4+
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **开发语言**: JavaScript (ES6+)

## 📦 项目结构

```
src/
├── components/          # 共享组件
│   ├── BottomNavigation.vue    # 底部导航
│   ├── CategoryCard.vue        # 分类卡片
│   ├── SearchBar.vue          # 搜索栏
│   └── ToolCard.vue           # 工具卡片
├── views/              # 页面组件
│   ├── Home.vue              # 首页
│   ├── Search.vue            # 搜索页
│   ├── Categories.vue        # 分类页
│   ├── CategoryDetail.vue    # 分类详情
│   ├── ToolDetail.vue        # 工具详情
│   ├── Favorites.vue         # 收藏页
│   ├── Profile.vue           # 个人中心
│   └── NotFound.vue          # 404页面
├── stores/             # Pinia状态管理
│   └── toolStore.js          # 工具数据状态
├── services/           # API服务
│   └── toolService.js        # 工具服务
├── data/              # 模拟数据
│   └── mockData.js           # 模拟数据
├── router/            # 路由配置
│   └── index.js              # 路由定义
├── assets/            # 静态资源
├── utils/             # 工具函数
├── App.vue            # 根组件
├── main.js            # 应用入口
└── style.css          # 全局样式
```

## 🚀 快速开始

### 环境要求

- Node.js 16.0+
- npm 7.0+ 或 yarn 1.22+

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

应用将在 `http://localhost:3000` 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 📱 功能介绍

### 主要功能

1. **首页**: 展示工具分类、热门工具、最新工具、推荐工具
2. **搜索**: 支持关键词搜索、分类筛选、多种排序方式
3. **分类**: 按分类浏览工具，查看分类统计信息
4. **工具详情**: 详细的工具信息、功能特性、相关推荐
5. **收藏**: 收藏喜欢的工具，本地存储管理
6. **个人中心**: 用户统计、浏览历史、设置选项

### 设计特色

- **紫色主题**: 主色调 #6B46C1，辅助色 #E9D5FF
- **卡片设计**: 现代化的卡片布局，阴影和圆角设计
- **渐变效果**: 精美的渐变背景和按钮效果
- **动画交互**: 流畅的过渡动画和悬停效果
- **响应式**: 完美适配手机、平板、桌面端

## 🎨 主题定制

项目使用 TailwindCSS 进行样式管理，主题色彩定义在 `tailwind.config.js` 中：

```javascript
colors: {
  primary: {
    DEFAULT: '#6B46C1',
    // ... 其他色阶
  },
  secondary: {
    DEFAULT: '#E9D5FF',
    // ... 其他色阶
  }
}
```

## 📊 数据说明

项目使用模拟数据，包含：
- 6个工具分类
- 6个示例工具
- 完整的工具信息（名称、描述、评分、下载量等）
- 统计数据和相关推荐

## 🔧 配置说明

### Vite 配置
- 路径别名: `@` 指向 `src` 目录
- 开发服务器: 端口 3000，自动打开浏览器
- 构建优化: 代码分割、资源压缩

### ESLint 配置
- Vue 3 规则
- Prettier 集成
- 自动修复

## 📝 开发规范

- 使用 Composition API
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case
- 提交信息遵循 Conventional Commits

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [ArcoDesign](https://arco.design/) - 企业级设计语言和 Vue 组件库
- [TailwindCSS](https://tailwindcss.com/) - 实用优先的 CSS 框架
- [Vite](https://vitejs.dev/) - 下一代前端构建工具

---

**工具广场** - 发现和分享优质工具的平台 🛠️✨
