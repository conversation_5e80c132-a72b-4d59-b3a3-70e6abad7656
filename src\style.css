@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式 */
@layer base {
  html {
    font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  body {
    margin: 0;
    padding: 0;
    background-color: #f8fafc;
    color: #1e293b;
    overflow-x: hidden;
  }
  
  * {
    box-sizing: border-box;
  }
}

/* 自定义组件样式 */
@layer components {
  .tool-card {
    @apply bg-white rounded-2xl shadow-sm border border-gray-100 p-4 transition-all duration-300 hover:shadow-lg hover:scale-105;
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, #6B46C1 0%, #8B5CF6 50%, #A855F7 100%);
  }
  
  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
  }
  
  .mobile-safe-area {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* ArcoDesign 主题定制 */
:root {
  --color-primary-6: #6B46C1;
  --color-primary-5: #7C3AED;
  --color-primary-4: #8B5CF6;
  --color-primary-3: #A78BFA;
  --color-primary-2: #C4B5FD;
  --color-primary-1: #DDD6FE;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .container {
    @apply px-4;
  }
  
  .grid-responsive {
    @apply grid-cols-1 sm:grid-cols-2;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 额外的移动端优化 */
@media (max-width: 640px) {
  .container {
    @apply px-3;
  }

  /* 优化触摸目标大小 */
  button, .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* 优化文字大小 */
  .text-xs {
    font-size: 0.75rem;
  }

  .text-sm {
    font-size: 0.875rem;
  }
}

/* 加载动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 卡片悬停动画 */
@keyframes cardHover {
  0% {
    transform: translateY(0) scale(1);
  }
  100% {
    transform: translateY(-4px) scale(1.02);
  }
}

.card-hover:hover {
  animation: cardHover 0.3s ease-out forwards;
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(135deg, #6B46C1 0%, #8B5CF6 50%, #A855F7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
