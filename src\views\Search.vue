<template>
  <div class="search-page min-h-screen bg-gray-50">
    <!-- 搜索头部 -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-30">
      <div class="max-w-4xl mx-auto px-4 py-4">
        <SearchBar 
          :placeholder="'搜索工具...'"
          @search="handleSearch"
          @input="handleInput"
          :show-hot-tags="!hasSearched"
        />
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 py-6">
      <!-- 搜索结果为空时显示 -->
      <div v-if="!hasSearched" class="text-center py-12">
        <div class="text-6xl mb-4">🔍</div>
        <h2 class="text-xl font-semibold text-gray-900 mb-2">开始搜索工具</h2>
        <p class="text-gray-600">输入关键词或选择热门标签开始搜索</p>
      </div>

      <!-- 搜索加载状态 -->
      <div v-else-if="searchLoading" class="text-center py-12">
        <a-spin size="large" />
        <div class="mt-4 text-gray-600">正在搜索...</div>
      </div>

      <!-- 搜索结果 -->
      <div v-else>
        <!-- 搜索结果头部 -->
        <div class="flex items-center justify-between mb-6">
          <div>
            <h2 class="text-xl font-semibold text-gray-900">
              搜索结果
              <span v-if="searchKeyword" class="text-primary">
                "{{ searchKeyword }}"
              </span>
            </h2>
            <p class="text-gray-600 text-sm mt-1">
              找到 {{ pagination.total }} 个相关工具
            </p>
          </div>
          
          <!-- 排序选择 -->
          <a-select
            v-model="sortBy"
            :style="{ width: '120px' }"
            @change="handleSortChange"
          >
            <a-option value="relevance">相关度</a-option>
            <a-option value="rating">评分</a-option>
            <a-option value="downloads">下载量</a-option>
            <a-option value="latest">最新</a-option>
          </a-select>
        </div>

        <!-- 筛选器 -->
        <div class="mb-6">
          <div class="flex flex-wrap gap-2">
            <button
              @click="selectedCategory = null"
              :class="[
                'px-3 py-1 rounded-full text-sm font-medium transition-colors',
                !selectedCategory 
                  ? 'bg-primary text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              ]"
            >
              全部分类
            </button>
            <button
              v-for="category in categories"
              :key="category.id"
              @click="selectedCategory = category.id"
              :class="[
                'px-3 py-1 rounded-full text-sm font-medium transition-colors',
                selectedCategory === category.id 
                  ? 'bg-primary text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              ]"
            >
              {{ category.name }}
            </button>
          </div>
        </div>

        <!-- 搜索结果列表 -->
        <div v-if="searchResults.length > 0">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <ToolCard
              v-for="tool in searchResults"
              :key="tool.id"
              :tool="tool"
            />
          </div>

          <!-- 分页 -->
          <div v-if="pagination.totalPages > 1" class="flex justify-center">
            <a-pagination
              v-model:current="pagination.page"
              :total="pagination.total"
              :page-size="pagination.pageSize"
              :show-total="true"
              :show-jumper="true"
              @change="handlePageChange"
            />
          </div>
        </div>

        <!-- 无搜索结果 -->
        <div v-else class="text-center py-12">
          <div class="text-6xl mb-4">😔</div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">没有找到相关工具</h3>
          <p class="text-gray-600 mb-4">试试其他关键词或浏览分类</p>
          <router-link
            to="/categories"
            class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            浏览分类
            <icon-right class="w-4 h-4 ml-2" />
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useToolStore } from '@/stores/toolStore.js'
import { IconRight } from '@arco-design/web-vue/es/icon'
import SearchBar from '@/components/SearchBar.vue'
import ToolCard from '@/components/ToolCard.vue'

const route = useRoute()
const router = useRouter()
const toolStore = useToolStore()

// 响应式数据
const searchKeyword = ref('')
const selectedCategory = ref(null)
const sortBy = ref('relevance')
const hasSearched = ref(false)

// 计算属性
const categories = computed(() => toolStore.categories)
const searchResults = computed(() => toolStore.searchResults)
const searchLoading = computed(() => toolStore.searchLoading)
const pagination = computed(() => toolStore.pagination)

// 处理搜索
const handleSearch = async (keyword) => {
  searchKeyword.value = keyword
  hasSearched.value = true
  await performSearch()
}

// 处理输入
const handleInput = (value) => {
  if (!value) {
    hasSearched.value = false
    toolStore.clearSearch()
  }
}

// 执行搜索
const performSearch = async () => {
  await toolStore.searchTools(
    searchKeyword.value,
    selectedCategory.value,
    pagination.value.page
  )
  
  // 更新URL
  const query = { q: searchKeyword.value }
  if (selectedCategory.value) {
    query.category = selectedCategory.value
  }
  if (sortBy.value !== 'relevance') {
    query.sort = sortBy.value
  }
  
  router.replace({ query })
}

// 处理排序变化
const handleSortChange = () => {
  if (hasSearched.value) {
    performSearch()
  }
}

// 处理分页变化
const handlePageChange = (page) => {
  if (hasSearched.value) {
    performSearch()
  }
}

// 监听分类选择变化
watch(selectedCategory, () => {
  if (hasSearched.value) {
    toolStore.pagination.page = 1
    performSearch()
  }
})

// 页面初始化
onMounted(() => {
  const query = route.query
  if (query.q) {
    searchKeyword.value = query.q
    hasSearched.value = true
    if (query.category) {
      selectedCategory.value = parseInt(query.category)
    }
    if (query.sort) {
      sortBy.value = query.sort
    }
    performSearch()
  }
})

// 设置页面名称用于keep-alive
defineOptions({
  name: 'Search'
})
</script>

<style scoped>
.search-page {
  min-height: 100vh;
}
</style>
