// 工具分类数据
export const categories = [
  { id: 1, name: '效率工具', icon: '⚡', color: '#6B46C1', count: 45 },
  { id: 2, name: '设计工具', icon: '🎨', color: '#EC4899', count: 32 },
  { id: 3, name: '开发工具', icon: '💻', color: '#10B981', count: 28 },
  { id: 4, name: '写作工具', icon: '✍️', color: '#F59E0B', count: 19 },
  { id: 5, name: '学习工具', icon: '📚', color: '#3B82F6', count: 24 },
  { id: 6, name: '娱乐工具', icon: '🎮', color: '#8B5CF6', count: 15 }
]

// 工具数据
export const tools = [
  {
    id: 1,
    name: 'Figma',
    description: '协作式界面设计工具，支持实时协作和原型制作',
    category: '设计工具',
    categoryId: 2,
    icon: '🎨',
    rating: 4.8,
    downloads: 1200000,
    tags: ['设计', '协作', '原型', 'UI/UX'],
    features: ['实时协作', '组件系统', '原型制作', '开发者交接'],
    screenshots: ['/images/figma-1.jpg', '/images/figma-2.jpg'],
    developer: 'Figma Inc.',
    version: '1.0.0',
    size: '125MB',
    lastUpdate: '2024-01-15',
    website: 'https://figma.com',
    isFree: true,
    isPremium: false
  },
  {
    id: 2,
    name: 'Notion',
    description: '集笔记、知识库、项目管理于一体的全能工作空间',
    category: '效率工具',
    categoryId: 1,
    icon: '📝',
    rating: 4.7,
    downloads: 2500000,
    tags: ['笔记', '项目管理', '协作', '知识库'],
    features: ['模块化编辑', '数据库功能', '团队协作', '模板系统'],
    screenshots: ['/images/notion-1.jpg', '/images/notion-2.jpg'],
    developer: 'Notion Labs',
    version: '2.18.0',
    size: '89MB',
    lastUpdate: '2024-01-20',
    website: 'https://notion.so',
    isFree: true,
    isPremium: true
  },
  {
    id: 3,
    name: 'VS Code',
    description: '轻量级但功能强大的源代码编辑器',
    category: '开发工具',
    categoryId: 3,
    icon: '💻',
    rating: 4.9,
    downloads: 5000000,
    tags: ['编程', '代码编辑', '调试', '扩展'],
    features: ['智能代码补全', '内置调试器', 'Git集成', '丰富扩展'],
    screenshots: ['/images/vscode-1.jpg', '/images/vscode-2.jpg'],
    developer: 'Microsoft',
    version: '1.85.2',
    size: '156MB',
    lastUpdate: '2024-01-18',
    website: 'https://code.visualstudio.com',
    isFree: true,
    isPremium: false
  },
  {
    id: 4,
    name: 'Typora',
    description: '简洁优雅的Markdown编辑器，所见即所得',
    category: '写作工具',
    categoryId: 4,
    icon: '✍️',
    rating: 4.6,
    downloads: 800000,
    tags: ['Markdown', '写作', '编辑器', '文档'],
    features: ['实时预览', '数学公式', '图表支持', '主题定制'],
    screenshots: ['/images/typora-1.jpg', '/images/typora-2.jpg'],
    developer: 'Typora',
    version: '1.7.6',
    size: '67MB',
    lastUpdate: '2024-01-12',
    website: 'https://typora.io',
    isFree: false,
    isPremium: true
  },
  {
    id: 5,
    name: 'Anki',
    description: '基于间隔重复算法的记忆卡片学习工具',
    category: '学习工具',
    categoryId: 5,
    icon: '📚',
    rating: 4.5,
    downloads: 1500000,
    tags: ['学习', '记忆', '卡片', '复习'],
    features: ['间隔重复', '多媒体卡片', '统计分析', '同步功能'],
    screenshots: ['/images/anki-1.jpg', '/images/anki-2.jpg'],
    developer: 'AnkiWeb',
    version: '2.1.66',
    size: '234MB',
    lastUpdate: '2024-01-10',
    website: 'https://apps.ankiweb.net',
    isFree: true,
    isPremium: false
  },
  {
    id: 6,
    name: 'Spotify',
    description: '全球领先的音乐流媒体平台',
    category: '娱乐工具',
    categoryId: 6,
    icon: '🎵',
    rating: 4.4,
    downloads: 10000000,
    tags: ['音乐', '播放器', '流媒体', '播放列表'],
    features: ['海量音乐库', '个性化推荐', '离线下载', '社交分享'],
    screenshots: ['/images/spotify-1.jpg', '/images/spotify-2.jpg'],
    developer: 'Spotify AB',
    version: '1.2.26',
    size: '178MB',
    lastUpdate: '2024-01-22',
    website: 'https://spotify.com',
    isFree: true,
    isPremium: true
  }
]

// 热门工具
export const popularTools = tools.slice(0, 4)

// 最新工具
export const latestTools = [...tools].sort((a, b) => new Date(b.lastUpdate) - new Date(a.lastUpdate)).slice(0, 4)

// 推荐工具
export const recommendedTools = tools.filter(tool => tool.rating >= 4.7)

// 统计数据
export const stats = {
  totalTools: tools.length,
  totalDownloads: tools.reduce((sum, tool) => sum + tool.downloads, 0),
  totalCategories: categories.length,
  averageRating: (tools.reduce((sum, tool) => sum + tool.rating, 0) / tools.length).toFixed(1)
}
