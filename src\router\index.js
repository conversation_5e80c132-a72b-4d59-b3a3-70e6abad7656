import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('@/views/Home.vue'),
      meta: {
        title: '工具广场',
        keepAlive: true
      }
    },
    {
      path: '/search',
      name: 'search',
      component: () => import('@/views/Search.vue'),
      meta: {
        title: '搜索工具',
        keepAlive: true
      }
    },
    {
      path: '/categories',
      name: 'categories',
      component: () => import('@/views/Categories.vue'),
      meta: {
        title: '工具分类',
        keepAlive: true
      }
    },
    {
      path: '/category/:id',
      name: 'category-detail',
      component: () => import('@/views/CategoryDetail.vue'),
      meta: {
        title: '分类详情'
      }
    },
    {
      path: '/tool/:id',
      name: 'tool-detail',
      component: () => import('@/views/ToolDetail.vue'),
      meta: {
        title: '工具详情'
      }
    },
    {
      path: '/favorites',
      name: 'favorites',
      component: () => import('@/views/Favorites.vue'),
      meta: {
        title: '我的收藏',
        keepAlive: true
      }
    },
    {
      path: '/profile',
      name: 'profile',
      component: () => import('@/views/Profile.vue'),
      meta: {
        title: '个人中心',
        keepAlive: true
      }
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: () => import('@/views/NotFound.vue'),
      meta: {
        title: '页面不存在'
      }
    }
  ],
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 工具广场`
  }
  
  next()
})

export default router
