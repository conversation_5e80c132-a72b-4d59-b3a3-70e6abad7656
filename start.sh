#!/bin/bash

echo ""
echo "========================================"
echo "   🛠️ 工具广场 - Tool Plaza"
echo "========================================"
echo ""
echo "正在检查环境..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 未检测到 Node.js，请先安装 Node.js 16.0+"
    echo "下载地址: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js 已安装"
node --version

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo ""
    echo "📦 正在安装项目依赖..."
    echo "这可能需要几分钟时间，请耐心等待..."
    echo ""
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败，请检查网络连接"
        exit 1
    fi
    echo "✅ 依赖安装完成"
else
    echo "✅ 项目依赖已存在"
fi

echo ""
echo "🚀 正在启动开发服务器..."
echo "应用将在浏览器中自动打开"
echo "如果没有自动打开，请访问: http://localhost:3000"
echo ""
echo "按 Ctrl+C 可以停止服务器"
echo ""

# 启动开发服务器
npm run dev
