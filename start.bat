@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    🛠️ 工具广场 - Tool Plaza
echo ========================================
echo.
echo 正在检查环境...

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到 Node.js，请先安装 Node.js 16.0+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js 已安装
node --version

:: 检查是否已安装依赖
if not exist "node_modules" (
    echo.
    echo 📦 正在安装项目依赖...
    echo 这可能需要几分钟时间，请耐心等待...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 项目依赖已存在
)

echo.
echo 🚀 正在启动开发服务器...
echo 应用将在浏览器中自动打开
echo 如果没有自动打开，请访问: http://localhost:3000
echo.
echo 按 Ctrl+C 可以停止服务器
echo.

:: 启动开发服务器
npm run dev

pause
