<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 主要内容区域 -->
    <main class="pb-16 md:pb-0">
      <router-view v-slot="{ Component, route }">
        <keep-alive :include="keepAliveComponents">
          <component :is="Component" :key="route.fullPath" />
        </keep-alive>
      </router-view>
    </main>

    <!-- 底部导航栏 (移动端) -->
    <BottomNavigation />

    <!-- 全局加载提示 -->
    <a-spin 
      v-if="globalLoading" 
      :loading="true" 
      class="fixed inset-0 z-50 bg-white bg-opacity-80 flex items-center justify-center"
    >
      <div class="text-center">
        <div class="text-primary text-lg font-medium mb-2">加载中...</div>
        <div class="text-gray-500 text-sm">正在为您准备精彩内容</div>
      </div>
    </a-spin>

    <!-- 全局错误提示 -->
    <a-modal
      v-model:visible="showErrorModal"
      title="出错了"
      :footer="false"
      @cancel="clearError"
    >
      <div class="text-center py-4">
        <div class="text-red-500 text-4xl mb-4">😵</div>
        <div class="text-gray-700 mb-4">{{ errorMessage }}</div>
        <a-button type="primary" @click="clearError">我知道了</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import { useToolStore } from '@/stores/toolStore.js'
import BottomNavigation from '@/components/BottomNavigation.vue'

const toolStore = useToolStore()

// 需要缓存的组件
const keepAliveComponents = ['Home', 'Search', 'Categories', 'Favorites', 'Profile']

// 全局加载状态
const globalLoading = computed(() => toolStore.loading)

// 错误处理
const showErrorModal = computed(() => !!toolStore.error)
const errorMessage = computed(() => toolStore.error || '未知错误')

const clearError = () => {
  toolStore.clearError()
}

// 初始化应用
onMounted(async () => {
  try {
    // 预加载基础数据
    await Promise.all([
      toolStore.fetchCategories(),
      toolStore.fetchStats(),
      toolStore.fetchAllTools()
    ])
  } catch (error) {
    console.error('应用初始化失败:', error)
  }
})
</script>

<style scoped>
/* 应用级别的样式 */
#app {
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移动端适配 */
@media (max-width: 768px) {
  main {
    padding-bottom: 4rem; /* 为底部导航栏留出空间 */
  }
}
</style>
