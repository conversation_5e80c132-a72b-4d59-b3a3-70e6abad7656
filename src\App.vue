<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- 主要内容区域 -->
    <main class="pb-16 md:pb-0">
      <router-view />
    </main>

    <!-- 底部导航栏 (移动端) -->
    <BottomNavigation />
  </div>
</template>

<script setup>
import BottomNavigation from '@/components/BottomNavigation.vue'
</script>

<style scoped>
/* 应用级别的样式 */
#app {
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移动端适配 */
@media (max-width: 768px) {
  main {
    padding-bottom: 4rem; /* 为底部导航栏留出空间 */
  }
}
</style>
