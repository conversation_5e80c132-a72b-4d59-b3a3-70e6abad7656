<template>
  <div class="favorites-page min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-4xl mx-auto px-4 py-6">
        <div class="text-center">
          <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            ❤️ 我的收藏
          </h1>
          <p class="text-gray-600">
            {{ favoriteTools.length }} 个收藏的工具
          </p>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 py-8">
      <!-- 收藏工具列表 -->
      <div v-if="favoriteTools.length > 0">
        <!-- 排序选择 -->
        <div class="flex items-center justify-between mb-6">
          <div class="text-sm text-gray-600">
            共收藏 {{ favoriteTools.length }} 个工具
          </div>
          <a-select
            v-model="sortBy"
            :style="{ width: '120px' }"
            @change="handleSortChange"
          >
            <a-option value="latest">最近收藏</a-option>
            <a-option value="rating">评分</a-option>
            <a-option value="downloads">下载量</a-option>
            <a-option value="name">名称</a-option>
          </a-select>
        </div>

        <!-- 工具网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ToolCard
            v-for="tool in sortedFavoriteTools"
            :key="tool.id"
            :tool="tool"
          />
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-16">
        <div class="text-8xl mb-6">💔</div>
        <h3 class="text-2xl font-semibold text-gray-900 mb-4">还没有收藏任何工具</h3>
        <p class="text-gray-600 mb-8 max-w-md mx-auto">
          浏览工具时点击心形图标即可收藏，收藏的工具会出现在这里
        </p>
        <div class="space-y-4">
          <router-link
            to="/"
            class="inline-flex items-center px-6 py-3 bg-primary text-white rounded-xl hover:bg-primary-600 transition-colors font-medium"
          >
            <icon-home class="w-5 h-5 mr-2" />
            去首页看看
          </router-link>
          <div class="text-center">
            <router-link
              to="/categories"
              class="inline-flex items-center px-4 py-2 text-primary hover:text-primary-600 transition-colors font-medium"
            >
              <icon-apps class="w-5 h-5 mr-2" />
              浏览分类
            </router-link>
          </div>
        </div>
      </div>

      <!-- 收藏统计 -->
      <div v-if="favoriteTools.length > 0" class="mt-12">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 收藏统计</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="bg-white rounded-xl p-4 text-center shadow-sm">
            <div class="text-2xl font-bold text-primary">{{ favoriteTools.length }}</div>
            <div class="text-sm text-gray-600">总收藏</div>
          </div>
          <div class="bg-white rounded-xl p-4 text-center shadow-sm">
            <div class="text-2xl font-bold text-green-600">{{ freeToolsCount }}</div>
            <div class="text-sm text-gray-600">免费工具</div>
          </div>
          <div class="bg-white rounded-xl p-4 text-center shadow-sm">
            <div class="text-2xl font-bold text-yellow-600">{{ averageRating }}</div>
            <div class="text-sm text-gray-600">平均评分</div>
          </div>
          <div class="bg-white rounded-xl p-4 text-center shadow-sm">
            <div class="text-2xl font-bold text-blue-600">{{ mostPopularCategory }}</div>
            <div class="text-sm text-gray-600">最爱分类</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useToolStore } from '@/stores/toolStore.js'
import { IconHome, IconApps } from '@arco-design/web-vue/es/icon'
import ToolCard from '@/components/ToolCard.vue'

const toolStore = useToolStore()

// 响应式数据
const sortBy = ref('latest')

// 计算属性
const favoriteTools = computed(() => toolStore.favoriteTools)

// 排序后的收藏工具
const sortedFavoriteTools = computed(() => {
  const tools = [...favoriteTools.value]
  
  switch (sortBy.value) {
    case 'rating':
      return tools.sort((a, b) => b.rating - a.rating)
    case 'downloads':
      return tools.sort((a, b) => b.downloads - a.downloads)
    case 'name':
      return tools.sort((a, b) => a.name.localeCompare(b.name))
    case 'latest':
    default:
      // 按收藏顺序（最近收藏的在前）
      return tools.reverse()
  }
})

// 统计数据
const freeToolsCount = computed(() => {
  return favoriteTools.value.filter(tool => tool.isFree).length
})

const averageRating = computed(() => {
  if (favoriteTools.value.length === 0) return '0.0'
  const total = favoriteTools.value.reduce((sum, tool) => sum + tool.rating, 0)
  return (total / favoriteTools.value.length).toFixed(1)
})

const mostPopularCategory = computed(() => {
  if (favoriteTools.value.length === 0) return '-'
  
  const categoryCount = {}
  favoriteTools.value.forEach(tool => {
    categoryCount[tool.category] = (categoryCount[tool.category] || 0) + 1
  })
  
  const mostPopular = Object.entries(categoryCount)
    .sort(([,a], [,b]) => b - a)[0]
  
  return mostPopular ? mostPopular[0] : '-'
})

// 处理排序变化
const handleSortChange = () => {
  // 排序在计算属性中处理
}

// 设置页面名称用于keep-alive
defineOptions({
  name: 'Favorites'
})
</script>

<style scoped>
.favorites-page {
  min-height: 100vh;
}
</style>
