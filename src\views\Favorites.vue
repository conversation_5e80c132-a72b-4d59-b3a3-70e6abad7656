<template>
  <div class="favorites-page min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white border-b border-gray-200">
      <div class="max-w-4xl mx-auto px-4 py-6">
        <div class="text-center">
          <h1 class="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
            ❤️ 我的收藏
          </h1>
          <p class="text-gray-600">
            收藏你喜欢的工具
          </p>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="max-w-4xl mx-auto px-4 py-8">
      <div class="text-center py-16">
        <div class="text-8xl mb-6">💔</div>
        <h3 class="text-2xl font-semibold text-gray-900 mb-4">还没有收藏任何工具</h3>
        <p class="text-gray-600 mb-8 max-w-md mx-auto">
          浏览工具时点击心形图标即可收藏，收藏的工具会出现在这里
        </p>
        <div class="space-y-4">
          <router-link
            to="/"
            class="inline-flex items-center px-6 py-3 bg-purple-600 text-white rounded-xl hover:bg-purple-700 transition-colors font-medium"
          >
            🏠 去首页看看
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 设置页面名称用于keep-alive
defineOptions({
  name: 'Favorites'
})
</script>

<style scoped>
.favorites-page {
  min-height: 100vh;
}
</style>
