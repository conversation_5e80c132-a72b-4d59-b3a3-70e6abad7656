import { defineStore } from 'pinia'
import { toolService } from '@/services/toolService.js'

export const useToolStore = defineStore('tool', {
  state: () => ({
    // 工具数据
    tools: [],
    currentTool: null,
    popularTools: [],
    latestTools: [],
    recommendedTools: [],
    relatedTools: [],
    
    // 分类数据
    categories: [],
    currentCategory: null,
    
    // 搜索相关
    searchKeyword: '',
    searchResults: [],
    searchLoading: false,
    
    // 分页信息
    pagination: {
      page: 1,
      pageSize: 10,
      total: 0,
      totalPages: 0
    },
    
    // 加载状态
    loading: false,
    error: null,
    
    // 统计数据
    stats: {
      totalTools: 0,
      totalDownloads: 0,
      totalCategories: 0,
      averageRating: 0
    },
    
    // 用户相关
    favorites: JSON.parse(localStorage.getItem('tool-favorites') || '[]'),
    viewHistory: JSON.parse(localStorage.getItem('tool-history') || '[]')
  }),

  getters: {
    // 获取收藏的工具
    favoriteTools: (state) => {
      return state.tools.filter(tool => state.favorites.includes(tool.id))
    },
    
    // 检查工具是否已收藏
    isFavorite: (state) => {
      return (toolId) => state.favorites.includes(toolId)
    },
    
    // 获取浏览历史工具
    historyTools: (state) => {
      return state.viewHistory
        .map(id => state.tools.find(tool => tool.id === id))
        .filter(Boolean)
        .slice(0, 10)
    }
  },

  actions: {
    // 获取所有工具
    async fetchAllTools() {
      this.loading = true
      this.error = null
      try {
        const response = await toolService.getAllTools()
        if (response.success) {
          this.tools = response.data
        }
      } catch (error) {
        this.error = error.message
      } finally {
        this.loading = false
      }
    },

    // 获取工具详情
    async fetchToolDetail(id) {
      this.loading = true
      this.error = null
      try {
        const response = await toolService.getToolById(id)
        if (response.success) {
          this.currentTool = response.data
          this.addToHistory(parseInt(id))
        } else {
          this.error = response.message
        }
      } catch (error) {
        this.error = error.message
      } finally {
        this.loading = false
      }
    },

    // 搜索工具
    async searchTools(keyword, categoryId = null, page = 1) {
      this.searchLoading = true
      this.error = null
      try {
        const response = await toolService.searchTools(keyword, categoryId, page, this.pagination.pageSize)
        if (response.success) {
          this.searchResults = response.data
          this.searchKeyword = keyword
          this.pagination = {
            page: response.page,
            pageSize: response.pageSize,
            total: response.total,
            totalPages: response.totalPages
          }
        }
      } catch (error) {
        this.error = error.message
      } finally {
        this.searchLoading = false
      }
    },

    // 获取分类列表
    async fetchCategories() {
      try {
        const response = await toolService.getCategories()
        if (response.success) {
          this.categories = response.data
        }
      } catch (error) {
        this.error = error.message
      }
    },

    // 根据分类获取工具
    async fetchToolsByCategory(categoryId, page = 1) {
      this.loading = true
      this.error = null
      try {
        const response = await toolService.getToolsByCategory(categoryId, page, this.pagination.pageSize)
        if (response.success) {
          this.tools = response.data
          this.currentCategory = this.categories.find(cat => cat.id === parseInt(categoryId))
          this.pagination = {
            page: response.page,
            pageSize: response.pageSize,
            total: response.total,
            totalPages: response.totalPages
          }
        }
      } catch (error) {
        this.error = error.message
      } finally {
        this.loading = false
      }
    },

    // 获取热门工具
    async fetchPopularTools() {
      try {
        const response = await toolService.getPopularTools()
        if (response.success) {
          this.popularTools = response.data
        }
      } catch (error) {
        this.error = error.message
      }
    },

    // 获取最新工具
    async fetchLatestTools() {
      try {
        const response = await toolService.getLatestTools()
        if (response.success) {
          this.latestTools = response.data
        }
      } catch (error) {
        this.error = error.message
      }
    },

    // 获取推荐工具
    async fetchRecommendedTools() {
      try {
        const response = await toolService.getRecommendedTools()
        if (response.success) {
          this.recommendedTools = response.data
        }
      } catch (error) {
        this.error = error.message
      }
    },

    // 获取相关工具
    async fetchRelatedTools(toolId) {
      try {
        const response = await toolService.getRelatedTools(toolId)
        if (response.success) {
          this.relatedTools = response.data
        }
      } catch (error) {
        this.error = error.message
      }
    },

    // 获取统计数据
    async fetchStats() {
      try {
        const response = await toolService.getStats()
        if (response.success) {
          this.stats = response.data
        }
      } catch (error) {
        this.error = error.message
      }
    },

    // 添加到收藏
    toggleFavorite(toolId) {
      const index = this.favorites.indexOf(toolId)
      if (index > -1) {
        this.favorites.splice(index, 1)
      } else {
        this.favorites.push(toolId)
      }
      localStorage.setItem('tool-favorites', JSON.stringify(this.favorites))
    },

    // 添加到浏览历史
    addToHistory(toolId) {
      const index = this.viewHistory.indexOf(toolId)
      if (index > -1) {
        this.viewHistory.splice(index, 1)
      }
      this.viewHistory.unshift(toolId)
      this.viewHistory = this.viewHistory.slice(0, 20) // 只保留最近20个
      localStorage.setItem('tool-history', JSON.stringify(this.viewHistory))
    },

    // 清空搜索结果
    clearSearch() {
      this.searchResults = []
      this.searchKeyword = ''
      this.pagination = {
        page: 1,
        pageSize: 10,
        total: 0,
        totalPages: 0
      }
    },

    // 重置错误状态
    clearError() {
      this.error = null
    }
  }
})
